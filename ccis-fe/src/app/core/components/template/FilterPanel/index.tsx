import React, { ChangeEvent } from "react";
import Filter from "@components/common/Filter";
import TextField from "@components/form/TextField";
import Select from "@components/form/Select";

interface FilterPanelProps {
  filters: any;
  resetCounter: number;
  onSearch: (event: ChangeEvent<HTMLInputElement>) => void;
  onDateFromChange: (event: ChangeEvent<HTMLInputElement>) => void;
  onDateToChange: (event: ChangeEvent<HTMLInputElement>) => void;
  onDivisionChange: (event: ChangeEvent<HTMLSelectElement>) => void;
  onTypeChange: (event: ChangeEvent<HTMLSelectElement>) => void;
  onClearAll: () => void;
  divisionOptions: Array<{ value: number; text: string }>;
  typeOptions: Array<{ value: number; text: string }>;
}

const FilterPanel: React.FC<FilterPanelProps> = ({
  filters,
  resetCounter,
  onSearch,
  onDateFromChange,
  onDateToChange,
  onDivision<PERSON>hange,
  onTypeChange,
  onClearAll,
  divisionOptions,
  typeOptions,
}) => {
  return (
    <Filter search={filters.filter} onChange={onSearch}>
      <div className="flex justify-end">
        <button
          className="text-primary text-xs btn-sm"
          type="button"
          onClick={onClearAll}
        >
          Clear All
        </button>
      </div>
      <div>
        <div className="grid grid-cols-2 gap-2">
          <div>
            <div className="text-xs">Date From</div>
            <TextField
              type="date"
              size="sm"
              value={filters.dateFrom}
              onChange={onDateFromChange}
            />
          </div>
          <div>
            <div className="text-xs">Date To</div>
            <TextField
              type="date"
              size="sm"
              value={filters.dateTo}
              onChange={onDateToChange}
            />
          </div>
        </div>
        <div className="flex gap-4">
          <div>
            <div className="text-xs">Division</div>
            <Select
              key={`division-${resetCounter}`}
              size="sm"
              options={divisionOptions}
              value={filters.divisionFilter}
              onChange={onDivisionChange}
            />
          </div>
          <div>
            <div className="text-xs">Type</div>
            <Select
              key={`type-${resetCounter}`}
              size="sm"
              options={typeOptions}
              value={filters.type}
              onChange={onTypeChange}
            />
          </div>
        </div>
      </div>
    </Filter>
  );
};

export default FilterPanel;