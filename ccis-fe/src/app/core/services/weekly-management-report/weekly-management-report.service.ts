import httpClient from "@clients/httpClient";
import { IDefaultParams } from "@interface/common.interface";
import { IAccomplishmentReportPayload } from "@interface/weekly-accomplishment-report.interface";

const accomplishmentReportApiResource = "accomplishment-report";
const issueTypeApiResource = "issue-type";
const taskGroupApiResource = "task-group";
const taskTypeApiResource = "task-type";

export const getAllAccomplishmentReportService = async (params: IDefaultParams) => {
  let queryParams = ``;

  if (params.relations) {
    queryParams += `relations=${params.relations}`;
  }
  if (params.id) {
    queryParams += `&id[eq]=${params.id}`;
  }
  if (params.page) {
    queryParams += `&page=${params.page}`;
  }
  if (params.pageSize) {
    queryParams += `&pageSize=${params.pageSize}`;
  }
  if (params.sort) {
    queryParams += `&sort=${params.sort}`;
  }
  if (params.condition) {
    queryParams += `&${params.condition}`;
  }
  if (params.filter) {
    queryParams += `&filter=${params.filter}`;
  }

  const fullUrl = `${accomplishmentReportApiResource}?${queryParams}`;
  return httpClient.get(fullUrl);
};

export const getAccomplishmentReportByIdService = async (params: IDefaultParams) => {
  let queryParams = "relations=accomplishmentTasks.issueType|accomplishmentTasks.taskType|accomplishmentTasks.taskGroup";

  if (params.page) {
    queryParams += `&page=${params.page}`;
  }
  if (params.pageSize) {
    queryParams += `&pageSize=${params.pageSize}`;
  }
  if (params.filter) {
    queryParams += `&filter=${params.filter}`;
  }
  if (params.statusFilter) {
    queryParams += `&statusFilter=${params.statusFilter}`;
  }
  if (params.parentFilter) {
    queryParams += `&parentFilter=${params.parentFilter}`;
  }
  if (params.dateFrom && params.dateTo) {
    queryParams += `&createdAt[between]=${params.dateFrom},${params.dateTo}`;
  }
  if (params.productTypeFilter) {
    queryParams += `&productTypeFilter=${params.productTypeFilter}`;
  }
  if (params.productCategoryFilter) {
    queryParams += `&productCategoryFilter=${params.productCategoryFilter}`;
  }
  if (params.targetMarketFilter) {
    queryParams += `&targetMarketFilter=${params.targetMarketFilter}`;
  }
  if (params.type) {
    queryParams += `&type=${params.type}`;
  }
  if (params.user) {
    queryParams += `&user=${params.user}`;
  }
  if (params.divisionFilter) {
    queryParams += `&divisionFilter=${params.divisionFilter}`;
  }
  if (params.areaFilter) {
    queryParams += `&areaFilter=${params.areaFilter}`;
  }
  if (params.condition) {
    queryParams += `&condition=${params.condition}`;
  }
  if (params.cooperative) {
    queryParams += `&cooperative=${params.cooperative}`;
  }
  if (params.formtypeFilter) {
    queryParams += `&formtypeFilter=${params.formtypeFilter}`;
  }
  if (params.nameFilter) {
    queryParams += `&nameFilter=${params.nameFilter}`;
  }
  if (params.eq !== undefined) {
    queryParams += `&eq=${params.eq}`;
  }
  if (params.sort) {
    queryParams += `&sort=${params.sort}`;
  }
  if (params.rows) {
    queryParams += `&rows=${params.rows}`;
  }
  if (params.role) {
    queryParams += `&role=${params.role}`;
  }

  return httpClient.get(`${accomplishmentReportApiResource}/${params.id}?${queryParams}`);
};

export const postAddAccomplishmentReportService = async (payload: IAccomplishmentReportPayload) => {
  return httpClient.post(`${accomplishmentReportApiResource}`, payload);
};

export const putUpdateAccomplishmentReportService = async (accomplishmentId: number, payload: IAccomplishmentReportPayload) => {
  return httpClient.put(`${accomplishmentReportApiResource}/${accomplishmentId}`, payload);
};

export const getIssueTypeService = async () => {
  return httpClient.get(`${issueTypeApiResource}`);
};

export const getTaskTypeService = async () => {
  return httpClient.get(`${taskTypeApiResource}`);
};

export const getTaskGroupService = async () => {
  return httpClient.get(`${taskGroupApiResource}`);
};

export const getLoggedInUserAccomplishmentReportService = async () => {
  let queryParams = "relations=accomplishmentTasks.issueType|accomplishmentTasks.taskType|accomplishmentTasks.taskGroup";
  let sort = "id,desc";

  return httpClient.get(`${accomplishmentReportApiResource}/me?${queryParams}&sort=${sort}`);
};

export const exportAccomplishmentReportService = async (accomplishmentReportId: number) => {
  return httpClient.get(`${accomplishmentReportApiResource}/${accomplishmentReportId}/export`, {
    responseType: "blob",
  });
};

export const destroyAccomplishmentReportService = async (accomplishmentReportId: number) => {
  return httpClient.delete(`${accomplishmentReportApiResource}/${accomplishmentReportId}`);
};
