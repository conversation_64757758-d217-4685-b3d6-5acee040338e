import { useState, useEffect, useMemo, useCallback } from "react";
import { useSelector } from "react-redux";
import { RootState } from "@state/reducer";
import { useTransmittalFormActions } from "@state/reducer/form-inventory-transmittal";
import { useIncomingReceivedFormActions } from "@state/reducer/form-inventory-incoming-received-form";
import { IDefaultParams } from "@interface/common.interface";
import { FormStatus, RoleType } from "@enums/form-status";
import { toast } from "react-toastify";
import { UserRoles } from "@interface/routes.interface";

interface UseRoleBasedInventoryDataProps {
  filters: IDefaultParams;
  userRole: RoleType;
  additionalFilters?: any;
  isReturned?: boolean;
  isVerifyList?: boolean;
}

export const useRoleBasedInventoryData = ({ filters, userRole, additionalFilters = {}, isReturned = false, isVerifyList = false }: UseRoleBasedInventoryDataProps) => {
  const [dataLoadingStates, setDataLoadingStates] = useState({
    primaryData: false,
    allLoaded: false,
  });

  // Memoize actions to prevent recreation on every render
  const transmittalActions = useTransmittalFormActions();
  const incomingActions = useIncomingReceivedFormActions();

  const { getIncomingReceivedForms } = incomingActions;
  const {
    getTransmittalFormsTrail,
    getCurrentUserFormTransmittalTrail,
    getLatestTransmittalForm,
    getCurrentUserOldestFormTransmittalTrail,
    getReturnedPads, // Add this action
  } = transmittalActions;

  // Get data from different sources based on user role
  const { data: INCOMING_FORMS } = useSelector((state: RootState) => state.formInventoryIncomingReceivedForms.getIncomingReceivedForms);
  const { data: FORMS } = useSelector((state: RootState) => state.formInventoryTransmittal.getTransmittalForms);
  const { data: USER_TRANSMITTALS } = useSelector((state: RootState) => state.formInventoryTransmittal.getCurrentUserFormTransmittalTrail);
  const RELEASED_FORMS = useSelector((state: RootState) => state.formInventoryTransmittal.transmittalForms);

  // Add selector for returned pads data
  const { data: RETURNED_PADS } = useSelector((state: RootState) => state.formInventoryTransmittal.getReturnedPads || { data: [] });

  const userId = useSelector((state: RootState) => state?.auth?.user.data?.id);
  const loading = useSelector((state: RootState) => state.formInventoryTransmittal.getTransmittalForms.loading);
  const incomingLoading = useSelector((state: RootState) => state.formInventoryIncomingReceivedForms.getIncomingReceivedForms.loading);
  const returnedPadsLoading = useSelector((state: RootState) => state.formInventoryTransmittal.getReturnedPads?.loading || false);

  const isDataLoading = loading || incomingLoading || returnedPadsLoading || !dataLoadingStates.allLoaded;

  // Memoize the loading state check to prevent unnecessary re-renders
  useEffect(() => {
    let primaryLoaded = false;

    if (isReturned) {
      // For returned forms, check different data sources
      switch (userRole) {
        case RoleType.IOC:
          primaryLoaded = RETURNED_PADS !== undefined && USER_TRANSMITTALS !== undefined;
          break;
        case RoleType.CHIEFCASHIER:
          primaryLoaded = RETURNED_PADS !== undefined && USER_TRANSMITTALS !== undefined;
          break;
        case RoleType.ADMININCOMING:
          primaryLoaded = RETURNED_PADS !== undefined && USER_TRANSMITTALS !== undefined;
          break;
        case RoleType.CLIFSA:
          primaryLoaded = RETURNED_PADS !== undefined && USER_TRANSMITTALS !== undefined;
          break;
        default:
          primaryLoaded = RETURNED_PADS !== undefined;
      }
    } else {
      // Original logic for non-returned forms
      switch (userRole) {
        case RoleType.IOC:
          if (isVerifyList) {
            primaryLoaded = INCOMING_FORMS !== undefined;
            break;
          }
          primaryLoaded = FORMS !== undefined && USER_TRANSMITTALS !== undefined && RELEASED_FORMS !== undefined;
          break;
        case RoleType.CHIEFCASHIER:
          primaryLoaded = INCOMING_FORMS !== undefined;
          break;
        case RoleType.ADMINOUTGOING:
          primaryLoaded = FORMS !== undefined && USER_TRANSMITTALS !== undefined && RELEASED_FORMS !== undefined;
          break;
        case RoleType.CLIFSA:
          primaryLoaded = FORMS !== undefined && USER_TRANSMITTALS !== undefined && RELEASED_FORMS !== undefined;
          break;
        case RoleType.GAM:
          primaryLoaded = FORMS !== undefined && USER_TRANSMITTALS !== undefined && RELEASED_FORMS !== undefined;
          break;
        default:
          primaryLoaded = FORMS !== undefined;
      }
    }

    setDataLoadingStates({
      primaryData: primaryLoaded,
      allLoaded: primaryLoaded,
    });
  }, [FORMS, USER_TRANSMITTALS, INCOMING_FORMS, RELEASED_FORMS, RETURNED_PADS, userRole, isReturned]);

  // Get data based on user role - memoized to prevent recalculation on every render
  const allFormsData = useMemo(() => {
    if (!dataLoadingStates.allLoaded) return [];

    if (isReturned) {
      // For returned forms, return data from returned pads and user transmittals
      switch (userRole) {
        case RoleType.IOC:
          return [...(RETURNED_PADS || []), ...(USER_TRANSMITTALS?.data || [])];
        case RoleType.CHIEFCASHIER:
          return [...(RETURNED_PADS || []), ...(USER_TRANSMITTALS?.data || [])];
        case RoleType.ADMININCOMING:
          return [...(RETURNED_PADS || []), ...(USER_TRANSMITTALS?.data || [])];
        case RoleType.CLIFSA:
          return [...(RETURNED_PADS || []), ...(USER_TRANSMITTALS?.data || [])];
        default:
          return RETURNED_PADS?.data || [];
      }
    } else {
      // Original logic for non-returned forms
      switch (userRole) {
        case RoleType.IOC:
          if (isVerifyList) {
            return INCOMING_FORMS?.data || [];
          } else {
            return [...(FORMS?.data || []), ...(USER_TRANSMITTALS?.data || []), ...(RELEASED_FORMS || [])];
          }
        case RoleType.CHIEFCASHIER:
          return INCOMING_FORMS?.data || [];
        case RoleType.ADMINOUTGOING:
          return [...(FORMS?.data || []), ...(USER_TRANSMITTALS?.data || []), ...(RELEASED_FORMS || [])];
        case RoleType.CLIFSA:
          return [...(FORMS?.data || []), ...(USER_TRANSMITTALS?.data || []), ...(RELEASED_FORMS || [])];
        case RoleType.GAM:
          return [...(FORMS?.data || []), ...(USER_TRANSMITTALS || []), ...(RELEASED_FORMS || [])];
        default:
          return FORMS?.data || [];
      }
    }
  }, [FORMS, USER_TRANSMITTALS, INCOMING_FORMS, RELEASED_FORMS, RETURNED_PADS, dataLoadingStates.allLoaded, userRole, isReturned]);

  // Use useCallback to memoize the fetchForms function
  const fetchForms = useCallback(async () => {
    try {
      setDataLoadingStates((prev) => ({ ...prev, allLoaded: false }));

      // Base payload with all filters for backend processing
      const basePayload = {
        filter: filters.filter,
        divisionFilter: filters.divisionFilter,
        type: filters.type,
        statusFilter: filters.statusFilter,
        dateFrom: filters.dateFrom,
        dateTo: filters.dateTo,
        page: filters.page,
        pageSize: filters.pageSize,
        ...additionalFilters,
      } as IDefaultParams;

      if (isReturned) {
        // API calls for returned forms
        switch (userRole) {
          case RoleType.CLIFSA:
            await Promise.all([
              getReturnedPads({
                ...basePayload,
                role: RoleType.GAM,
                statusFilter: `${FormStatus.RETURNED},${FormStatus.RECEIVED}`,
              }),
              getCurrentUserFormTransmittalTrail({
                ...basePayload,
                statusFilter: FormStatus.RETURNED,
                id: userId,
                eq: true,
              }),
            ]);
            break;

          case RoleType.ADMININCOMING:
            await Promise.all([
              getReturnedPads({
                ...basePayload,
                role: RoleType.CLIFSA,
                statusFilter: `${FormStatus.RETURNED},${FormStatus.RECEIVED}`,
              }),
              getCurrentUserFormTransmittalTrail({
                ...basePayload,
                statusFilter: FormStatus.RETURNED,
                id: userId,
                eq: true,
              }),
            ]);
            break;

          case RoleType.IOC:
            await Promise.all([
              getReturnedPads({
                ...basePayload,
                role: RoleType.ADMININCOMING,
                statusFilter: `${FormStatus.RETURNED},${FormStatus.RECEIVED}`,
              }),
              getCurrentUserFormTransmittalTrail({
                ...basePayload,
                statusFilter: FormStatus.RETURNED,
                id: userId,
                eq: true,
              }),
            ]);
            break;

          case RoleType.CHIEFCASHIER:
            await Promise.all([
              getReturnedPads({
                ...basePayload,
                role: RoleType.IOC,
                statusFilter: `${FormStatus.RETURNED},${FormStatus.RECEIVED}`,
              }),
              getCurrentUserFormTransmittalTrail({
                ...basePayload,
                statusFilter: FormStatus.RETURNED,
                id: userId,
                eq: true,
              }),
            ]);
            break;

          default:
            await getReturnedPads(basePayload);
        }
      } else {
        // Original API calls for non-returned forms
        switch (userRole) {
          case RoleType.IOC:
            if (isVerifyList) {
              await getIncomingReceivedForms(basePayload);
            } else {
              await Promise.all([
                getTransmittalFormsTrail({
                  ...basePayload,
                  role: RoleType.CHIEFCASHIER,
                  statusFilter: FormStatus.RELEASED,
                }),
                getLatestTransmittalForm({
                  ...basePayload,
                  role: RoleType.CHIEFCASHIER,
                  statusFilter: FormStatus.RECEIVED,
                }),
                getCurrentUserFormTransmittalTrail({
                  ...basePayload,
                  id: userId,
                  statusFilter: FormStatus.APPROVED,
                }),
              ]);
            }
            break;

          case RoleType.CHIEFCASHIER:
            await getIncomingReceivedForms(basePayload);
            break;

          case RoleType.ADMINOUTGOING:
            await Promise.all([
              getTransmittalFormsTrail({
                ...basePayload,
                role: UserRoles.ioc,
                statusFilter: FormStatus.RELEASED,
                id: userId,
              }),
              getLatestTransmittalForm({
                ...basePayload,
                role: UserRoles.ioc,
                statusFilter: FormStatus.RECEIVED,
                user: userId,
              }),
              getCurrentUserFormTransmittalTrail({
                ...basePayload,
                id: userId,
                statusFilter: FormStatus.APPROVED,
              }),
            ]);
            break;

          case RoleType.CLIFSA:
            await Promise.all([
              getTransmittalFormsTrail({
                ...basePayload,
                role: RoleType.ADMINOUTGOING,
                statusFilter: FormStatus.RELEASED,
                id: userId,
              }),
              getLatestTransmittalForm({
                ...basePayload,
                role: RoleType.ADMINOUTGOING,
                statusFilter: FormStatus.RECEIVED,
                user: userId,
              }),
              getCurrentUserFormTransmittalTrail({
                ...basePayload,
                id: userId,
                statusFilter: FormStatus.APPROVED,
              }),
            ]);
            break;

          case RoleType.GAM:
            await Promise.all([
              getTransmittalFormsTrail({
                ...basePayload,
                role: RoleType.CLIFSA,
                statusFilter: FormStatus.RELEASED,
                id: userId,
              }),
              getLatestTransmittalForm({
                ...basePayload,
                role: RoleType.CLIFSA,
                statusFilter: FormStatus.RECEIVED,
                user: userId,
              }),
              getCurrentUserOldestFormTransmittalTrail({
                ...basePayload,
                id: userId,
                statusFilter: FormStatus.RETURNED,
                eq: true,
              }),
            ]);
            break;

          default:
            await getTransmittalFormsTrail(basePayload);
        }
      }
    } catch (error) {
      toast.error(`Error fetching forms: ${String(error)}`);
    }
  }, [JSON.stringify(filters), JSON.stringify(additionalFilters), userRole, userId, isReturned]);

  return {
    allFormsData,
    isDataLoading,
    dataLoadingStates,
    fetchForms,
    userId,
  };
};
