import React, { ReactNode, useEffect } from "react";

import Tabs from "@components/common/Tabs";

import AllTab from "./components/tabs/AllTab";
import ForReceivingTab from "./components/tabs/ForReceivingTab";
import ReturnTab from "./components/tabs/ReturnTab";
import { clearData, hasKey } from "@helpers/storage";

const CashierNewForms: React.FC = () => {
  const headers: string[] = ["All", "For Receiving", "Return"];
  const contents: ReactNode[] = [<AllTab />, <ForReceivingTab />, <ReturnTab />];

  useEffect(() => {
    if (hasKey("currentPad")) {
      clearData("currentPad");
    }
  }, []);

  return (
    <div>
      <div className=" my-4 py-4 border-b border-zinc-300 w-full text-zinc-500">
        Dashboard / <span className="text-primary font-poppins-semibold ">SI | OR | PR FORMS</span>
      </div>
      <Tabs headers={headers} contents={contents} size="md" headerClass="w-52" fullWidthHeader={false} />
    </div>
  );
};

export default CashierNewForms;
