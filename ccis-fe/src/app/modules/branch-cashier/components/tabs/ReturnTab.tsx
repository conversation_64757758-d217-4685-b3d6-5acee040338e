import React, { ChangeEvent, useEffect, useState } from "react";
import { useDebouncedCallback } from "use-debounce";
import Filter from "@components/common/Filter";
import TextField from "@components/form/TextField";
import Table from "@components/common/Table";
import { RootState } from "@state/reducer";
import { useSelector } from "react-redux";
import ActionDropdown from "@components/common/ActionDropdown";
import Typography from "@components/common/Typography";
import { getTextStatusColor } from "@helpers/text";
import { IActions, IDefaultParams } from "@interface/common.interface";
import { TableColumn } from "react-data-table-component";
import { GoVersions } from "react-icons/go";
import { useNavigate } from "react-router-dom";
import { ROUTES } from "@constants/routes";
import { useTransmittalFormActions } from "@state/reducer/form-inventory-transmittal";
import { useDivisionActions } from "@state/reducer/form-inventory-utilities-divisions";
import { useFormTypeActions } from "@state/reducer/form-inventory-utilities-form-types";
import { useAreaActions } from "@state/reducer/utilities-areas";
import Select from "@components/form/Select";
import { FormStatus, RoleType } from "@enums/form-status";
import { IFormTransmittal } from "@interface/form-inventory.interface";
import { findItem } from "@helpers/array";

const ReturnTab: React.FC = () => {
  const [searchText, setSearchText] = useState<string | undefined>("");
  const [divisionFilter, setDivisionFilter] = useState<number | undefined>();
  const [type, setType] = useState<number | undefined>();
  const [resetCounter, setResetCounter] = useState(0);
  const [dateFrom, setDateFrom] = useState<string>("");
  const [dateTo, setDateTo] = useState<string>("");
  const [page, setPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);

  const { data: FORMS } = useSelector(
    (state: RootState) => state.formInventoryTransmittal.getTransmittalForms
  );
  const { getTransmittalFormsTrail } = useTransmittalFormActions();
  const loading = useSelector(
    (state: RootState) =>
      state.formInventoryTransmittal.getTransmittalForms.loading
  );

  const divisions = useSelector(
    (state: RootState) => state.formInventoryUtilitiesDivisions.divisions
  );
  const formTypes = useSelector(
    (state: RootState) => state.formInventoryUtilitiesFormTypes.formTypes
  );
  const area = useSelector((state: RootState) => state.utilitiesAreas.areas);
  const divisionOptions = [
    { value: 0, text: "Select Division" },
    ...useSelector(
      (state: RootState) => state.formInventoryUtilitiesDivisions.divisions
    ).map((value) => {
      return { value: value.id, text: value.divisionName };
    }),
  ];
  const typeOptions = [
    { value: 0, text: "Select Type" },
    ...useSelector(
      (state: RootState) => state.formInventoryUtilitiesFormTypes.formTypes
    ).map((value) => {
      return { value: value.id, text: value.formTypeName };
    }),
  ];

  const userId = useSelector((state: RootState) => state?.auth?.user.data?.id);
  const { getDivisions } = useDivisionActions();
  const { getFormTypes } = useFormTypeActions();
  const { getAreas } = useAreaActions();
  const filterDivision = "";
  const filterFormType = "";
  const filterArea = "";
  const navigate = useNavigate();

  const handleSearch = useDebouncedCallback(
    (event: ChangeEvent<HTMLInputElement>) => {
      // Extract the current input value from the event
      const value = event.target.value;
      setSearchText(value);
    },
    500
  );

  const handleDivisionChange = (event: ChangeEvent<HTMLSelectElement>) => {
    const value = parseInt(event.target.value);
    setDivisionFilter(value);
  };

  const handleTypeChange = (event: ChangeEvent<HTMLSelectElement>) => {
    const value = parseInt(event.target.value);
    setType(value);
  };

  const handleClearAll = () => {
    setSearchText("");
    setDivisionFilter(0);
    setDateFrom("");
    setDateTo("");
    setResetCounter((prev) => prev + 1);
  };

  const handleDateFromChange = (event: ChangeEvent<HTMLInputElement>) => {
    setDateFrom(event.target.value);
  };

  const handleDateToChange = (event: ChangeEvent<HTMLInputElement>) => {
    setDateTo(event.target.value);
  };

  const commonSetting = {
    sortable: true,
    reorder: true,
  };

  const getActionEvents = (Transmittal: any): IActions<any>[] => {
    const actions: IActions<any>[] = [
      {
        name: "View",
        event: () => {
          navigate(
            ROUTES.CASHIER.viewForReceivingForm.parse(
              Transmittal.id
            )
          );
        },
        icon: GoVersions,
        color: "primary",
      },
    ];
    return actions;
  };

  // Get data with RELEASED status and released by head cashier
  const columns: TableColumn<any>[] = [
    {
      name: "No.",
      cell: (row) => row.id,
      width: "80px",
      ...commonSetting,
    },
    {
      name: "Transmittal No.",
      cell: (row) => row.transmittalNumber,
      ...commonSetting,
    },
    {
      name: "ATP Number",
      cell: (row) => row.padAssignments?.[0]?.form?.atpNumber || "N/A",
      ...commonSetting,
    },
    {
      name: "Division",
      cell: (row) => {
        const divisionsList = Array.from(

          new Set(
            row.padAssignments
              ?.map((assignment: { form?: { divisionId?: number } }) => {
                const division = divisions.find(
                  (division) => division.id === assignment.form?.divisionId
                );
                return division ? division.divisionName : null;
              })
              .filter(Boolean) // Remove null values
          )
        );
        return divisionsList.length > 0 ? divisionsList.join(", ") : "N/A";
      },
      ...commonSetting,
    },
    {
      name: "Type",
      cell: (row) => {
        const uniqueFormTypes = Array.from(
          new Set(
            row.padAssignments?.map((assignment: { form?: { formTypeId?: number } }) => {
              const formType = formTypes.find(
                (type) => type.id === assignment.form?.formTypeId
              );
              return formType ? formType.formTypeCode : null;
            }).filter(Boolean) // Remove null values
          )
        );
        return uniqueFormTypes.length > 0 ? uniqueFormTypes.join(", ") : "N/A";
      },
      ...commonSetting,
    },
    {
        name: "Released Area",
        cell: (row) => (
          <div>
            {String(
              findItem(
                area,
                "id",
                Number(row?.releasedAreaId),
                "areaName"
              ) || "N/A"
            )}
          </div>
        ),
        ...commonSetting,
    },
    {
      name: "No. of Pads",
      cell: (row) => row.padAssignments?.length || "N/A",
      width: "120px",
      ...commonSetting,
    },
    {
      name: "Status",
      cell: (_row) => (
    <Typography size="xs" className={getTextStatusColor(FormStatus.NOT_YET_RECEIVED)}>
      {FormStatus.NOT_YET_RECEIVED.replace(/_/g, " ").toLowerCase().replace(/\b\w/g, (char) => char.toUpperCase())}
    </Typography>
      )
    },
    {
      name: (
        <Typography className="flex flex-1 justify-center !text-black !text-xs">
          Actions
        </Typography>
      ),
      cell: (row, rowIndex) => (
        <ActionDropdown
          actions={getActionEvents(row)}
          data={row}
          rowIndex={rowIndex}
        />
      ),
    },
  ];
  const handleRowsChange = (newPerPage: number, pagination: number) => {
    setPageSize(newPerPage);
    setPage(pagination);
  };

  const handlePaginate = (page: number) => {
    setPage(page);
  };
  const sortedTransmittal = FORMS?.data
  ?.slice()
  .sort((a: IFormTransmittal, b: IFormTransmittal) => {
    return Number(b.id) - Number(a.id); // Explicitly convert to number
  });

  const fetchForms = () => {
    const payload = {
      filter: searchText,
      page: page,
      pageSize: pageSize,
      divisionFilter: divisionFilter,
      type: type,
      role: `${RoleType.AREA_ADMIN},${RoleType.CHIEFCASHIER}`,
      id: userId,
    } as IDefaultParams;
    getTransmittalFormsTrail(payload);
  };

  useEffect(() => {
    fetchForms();
  }, [searchText, divisionFilter, type, page, pageSize, dateFrom, dateTo]);

  useEffect(() => {
    getDivisions({ filter: filterDivision });
    getFormTypes({ filter: filterFormType });
    getAreas({ filter: filterArea });
  }, []);


  return (
    <div className="p-4">
      <div className="mt-8">
        <div className="flex flex-row justify-between">
          <Filter search={searchText} onChange={handleSearch}>
            <div className="flex justify-end">
              <button
                className="text-primary text-xs btn-sm"
                type="button"
                onClick={handleClearAll}
              >
                Clear All
              </button>
            </div>
            <div>
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <div className="text-xs">Date From</div>
                  <TextField
                    type="date"
                    size="sm"
                    value={dateFrom}
                    onChange={handleDateFromChange}
                  />
                </div>
                <div>
                  <div className="text-xs">Date To</div>
                  <TextField
                    type="date"
                    size="sm"
                    value={dateTo}
                    onChange={handleDateToChange}
                  />
                </div>
              </div>
              <div className="flex gap-4">
                <div>
                  <div className="text-xs">Division</div>
                  <Select
                    key={`division-${resetCounter}`}
                    size="sm"
                    options={divisionOptions}
                    value={divisionFilter}
                    onChange={handleDivisionChange}
                  />
                </div>
                <div>
                  <div className="text-xs">Type</div>
                  <Select
                    key={`type-${resetCounter}`}
                    size="sm"
                    options={typeOptions}
                    value={type}
                    onChange={handleTypeChange}
                  />
                </div>
              </div>
            </div>
          </Filter>
        </div>
        <Table
        className="!min-h-[100%] h-[500px] border-[1px] border-zinc-300 mt-8"
        columns={columns}
        data={sortedTransmittal}
        paginationServer={true}
        paginationTotalRows={FORMS?.meta?.total}
        loading={loading}
        onChangeRowsPerPage={handleRowsChange}
        onPaginate={handlePaginate}
        searchable={false}
        multiSelect={false}
      />
      </div>
    </div>
  );
};

export default ReturnTab;
