
import React from "react";
import { useSelector } from "react-redux";
import { RootState } from "@state/store";
import Button from "@components/common/Button";

//For the chart
import { <PERSON>, Doughnut, Pie } from "react-chartjs-2";
import Outlabels from './energiency/chartjs-plugin-piechart-outlabels';
import { Chart as ChartJS, CategoryScale, ArcElement, LinearScale, BarElement, Title, Tooltip, Legend } from "chart.js";

//Images and Icons are stored here.
import { FiChevronRight } from "react-icons/fi";
import dashboard from "@assets/dashboard/male_user.gif";
import ColumnChart from "../IncomingOutgoingCashierDashboard.tsx/charts/columnChart";

// Register the components you plan to use
ChartJS.register(CategoryScale, LinearScale, BarElement, ArcElement, Title, Tooltip, Legend, Outlabels);

const SalesDashboard: React.FC = () => {
  //For getting the user data
  const user = useSelector((state: RootState) => state.auth?.user?.data);

  //Dummy data 
  const data = {
    labels: ["Jan", "Feb", "March", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"],
    datasets: [
      {
        label: "Registered Cooperatives 2024",
        data: [30, 21, 29, 40, 9, 20, 31, 50, 12, 31, 32, 25],
        backgroundColor: "#1213eb",
        borderRadius: 3,
        barThickness: 30, // Sets the exact width of each bar in pixels
        maxBarThickness: 40, // Sets the maximum width of the bar
        categoryPercentage: 0.8, // Controls the width of the category relative to the entire bar chart
        barPercentage: 0.9, // Controls the width of the bars relative to the category width
      },
    ],
  };

  const columnCategories = data.labels;
  const columnSeries = data.datasets.map(ds => ({ name: ds.label, data: ds.data }));
  const columnColors = data.datasets.map(ds =>
    Array.isArray(ds.backgroundColor) ? (ds.backgroundColor[0] as string) : (ds.backgroundColor as string || "#1213eb")
  );
  //Options for the chart
  const options = {
    responsive: true,
    plugins: {
      title: {
        display: false,
        text: "",
      },
    },
    scales: {
      y: {
        beginAtZero: true, // Ensures the y-axis starts at 0
        max: 60, // Set the maximum y-axis value
        ticks: {
          stepSize: 10, // Set the interval between ticks
        },
      },
    },
  };

  const data2 = {
    labels: ['Life Products','Non-Life Products','Micro Life Products','Micro Non-Life Products'],
    datasets: [{
      data: [500, 500, 200, 300],
      backgroundColor: ['#FFD000','#042882','#28A845','#6A31FF'],
      borderColor: '#fff',
      borderWidth: 4,
    }]
  };

  const options2 = {
    plugins: {
      title: { display: true, text: 'PRODUCTS SOLD', align: 'center', font: { size: 14, weight: 'bold' }, color: '#374151' },
      legend: { display: false },
      outlabels: {
        text: '%l %v',
        color: '#4b5563',
        stretch: 18, // length of connector
        lineColor: '#cbd5e1',
        lineWidth: 2,
        font: { resizable: true, minSize: 10, maxSize: 14 }
      }
    }
  };

  return (
    <div className="p-5">
      <div className="w-full flex items-start justify-between gap-6 px-5">
        <div className="flex-1 flex flex-col items-start">
          <span className="text-primary xl:text-4xl text-xl font-semibold">Welcome back, {user?.firstname}</span>
          <span className="xl:text-sm text-xs text-gray-700">Have a nice day at work.</span>
        </div>
        <div className="relative self-start ml-8 w-[20rem] h-[320px] overflow-hidden">
          <img src={dashboard} className="absolute inset-0 w-full h-full object-cover transform scale-[1.15] translate-y-[-12%] translate-x-[5%]" />
        </div>
      </div>
      <br />
      <div className="w-full flex py-5 text-2xl font-semibold translate-y-[-25%]">
        Overview
      </div>
      {/* PARENT SA MGA CHARTS */}
      <div className="w-full h-1/2">
        <div className="w-full h-full flex gap-4">
          {" "}
          <div className="w-2/3 h-full rounded-lg border border-zinc-100 shadow-md p-5"> 
            <div className="w-full flex justify-between items-center uppercase font-semibold text-zinc-500 p-5">
              Registered Cooperatives
            </div>
             <ColumnChart 
                scrollbar={true} 
                categories={columnCategories} 
                series={columnSeries} 
                colors={columnColors} 
                useGradient 
                title=" " 
                percentage={true}
                hideToolbar={true}
              />
          </div>
          <div className="w-1/3 h-[95%] flex items-center justify-center relative rounded-lg border border-zinc-100 shadow-md p-5">
            <Pie data={data2} options={options2}/>
          </div>
        </div>
      </div>

      <div className="h-1/4 w-full flex flex-col mt-10">
        <div className="mb-4 xl:text-xl text-sm flex justify-between items-center px-10">
          <div>Continue where you left off ...</div>
          <div className="underline text-sm text-primary cursor-pointer flex items-center justify-center">
            View all drafts <FiChevronRight size={20} />
          </div>
        </div>

        <div className="w-full flex flex-col items-center justify-between gap-2 ">
          <div className=" border-b border-zinc-300 xl:p-6 p-2 w-full rounded-md flex items-center justify-center text-sm">
            <div className=" w-1/5 flex items-center justify-center">January 11, 2024</div>
            <div className=" w-1/5 flex items-center justify-center ">Product Guidelines</div>
            <div className=" w-1/5 flex items-center justify-center">Customized</div>
            <div className=" w-1/5 flex items-center justify-center ">
              <div className="px-4 p-2 flex items-center justify-center rounded-full bg-zinc-200  ">Draft</div>
            </div>
            <div className=" w-1/5 flex items-center justify-center ">
              <Button classNames="btn px-8 p-2 flex items-center justify-center rounded-md bg-primary  text-white">View Details</Button>
            </div>
          </div>

          <div className=" border-b border-zinc-300 xl:p-6 p-2 w-full rounded-md flex items-center justify-center text-sm">
            <div className=" w-1/5 flex items-center justify-center">January 11, 2024</div>
            <div className=" w-1/5 flex items-center justify-center ">Product Guidelines</div>
            <div className=" w-1/5 flex items-center justify-center">Standard</div>
            <div className=" w-1/5 flex items-center justify-center ">
              <div className="px-4 p-2 flex items-center justify-center rounded-full bg-zinc-200  ">Draft</div>
            </div>
            <div className=" w-1/5 flex items-center justify-center ">
              <Button classNames="btn px-8 p-2 flex items-center justify-center rounded-md bg-primary  text-white">View Details</Button>
            </div>
          </div>

          <div className=" border-b border-zinc-300 xl:p-6 p-2 w-full rounded-md flex items-center justify-center text-sm">
            <div className=" w-1/5 flex items-center justify-center">January 11, 2024</div>
            <div className=" w-1/5 flex items-center justify-center ">Product Guidelines</div>
            <div className=" w-1/5 flex items-center justify-center">Customized</div>
            <div className=" w-1/5 flex items-center justify-center ">
              <div className="px-4 p-2 flex items-center justify-center rounded-full bg-zinc-200  ">Draft</div>
            </div>
            <div className=" w-1/5 flex items-center justify-center ">
              <Button classNames="btn px-8 p-2 flex items-center justify-center rounded-md bg-primary  text-white">View Details</Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SalesDashboard;
