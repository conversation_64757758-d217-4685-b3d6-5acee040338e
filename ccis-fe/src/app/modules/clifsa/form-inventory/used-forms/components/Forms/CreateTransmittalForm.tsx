import React, { useEffect, useState } from "react";
import DataTable, { TableColumn } from "react-data-table-component";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { RootState } from "@state/reducer";

import { useDivisionActions } from "@state/reducer/form-inventory-utilities-divisions";
import { useFormTypeActions } from "@state/reducer/form-inventory-utilities-form-types";
import { useAreaActions } from "@state/reducer/utilities-areas";
import { IDefaultParams } from "@interface/common.interface";
import { ROUTES } from "@constants/routes";
import Button from "@components/common/Button";
import { FormStatus, RoleType } from "@enums/form-status";
import { useTransmittalFormActions } from "@state/reducer/form-inventory-transmittal";
import { IFormTransmittal } from "@interface/form-inventory.interface";

interface SeriesTableProps {
  handleToggleFormModal: () => void;
  searchText?: string;
  divisionFilter?: number;
  onRowSelect?: (row: any) => void;
}

interface GetTransmittal {
  data: IFormTransmittal[];
  meta?: {
    total: number;
    page: number;
    pageSize: number;
  };
  loading: boolean;
  error: boolean;
}

const SeriesTransmittalTable: React.FC<SeriesTableProps> = ({ handleToggleFormModal, searchText = "", divisionFilter }) => {
  const navigate = useNavigate();
  const [selectedRow, setSelectedRow] = useState<any>(null);
  const { getReturnedPads } = useTransmittalFormActions();

  // Properly type the data and extract the array
  const getTransmittalData = useSelector((state: RootState) => state.formInventoryTransmittal.getReturnedPads) as GetTransmittal;

  const returnedPads: IFormTransmittal[] = getTransmittalData?.data || [];

  const divisions = useSelector((state: RootState) => state.formInventoryUtilitiesDivisions.divisions);
  const formTypes = useSelector((state: RootState) => state.formInventoryUtilitiesFormTypes.formTypes);
  const area = useSelector((state: RootState) => state.utilitiesAreas.areas);
  const loading = useSelector((state: RootState) => state.formInventoryTransmittal.getLatestTransmittalForm.loading);
  const userId = useSelector((state: RootState) => state?.auth?.user.data?.id);
  const { getDivisions } = useDivisionActions();
  const { getFormTypes } = useFormTypeActions();
  const { getAreas } = useAreaActions();

  const commonSetting = { sortable: true, reorder: true };

  const columns: TableColumn<IFormTransmittal>[] = [
    {
      name: "No.",
      cell: (row) => row.id,
      width: "80px",
      ...commonSetting,
    },
    {
      name: "Transmittal No.",
      cell: (row) => row.transmittalNumber,
      ...commonSetting,
    },
    {
      name: "Division",
      cell: (row) => {
        const divisionsList = Array.from(
          new Set(
            row.returnedPads
              ?.map((assignment: { form?: { divisionId?: number } }) => {
                const division = divisions.find((division) => division.id === assignment.form?.divisionId);
                return division ? division.divisionName : null;
              })
              .filter(Boolean) // Remove null values
          )
        );
        return divisionsList.length > 0 ? divisionsList.join(", ") : "N/A";
      },
      ...commonSetting,
    },
    {
      name: "Type",
      cell: (row) => {
        const uniqueFormTypes = Array.from(
          new Set(
            row.returnedPads
              ?.map((assignment: { form?: { formTypeId?: number } }) => {
                const formType = formTypes.find((type) => type.id === assignment.form?.formTypeId);
                return formType ? formType.formTypeName : null;
              })
              .filter(Boolean) // Remove null values
          )
        );
        return uniqueFormTypes.length > 0 ? uniqueFormTypes.join(", ") : "N/A";
      },
      ...commonSetting,
    },
    {
      name: "Released Area",
      cell: (row) => {
        const releasedArea = area.find((area) => area.id === row.returnedPads?.[0]?.form?.areaId);
        return releasedArea ? releasedArea.areaName : "N/A";
      },
      ...commonSetting,
    },
    {
      name: "No. of Pads",
      cell: (row) => row.returnedPads?.length || "N/A",
      width: "120px",
      ...commonSetting,
    },
  ];

  const fetchForms = () => {
    const payload = {
      filter: searchText,
      // divisionFilter: divisionFilter,
      // type: type,
      role: `${RoleType.CEC},${RoleType.ADMINSATELLITE}`,
      statusFilter: FormStatus.RECEIVED,
      user: userId,
    } as IDefaultParams;
    getReturnedPads(payload);
  };

  useEffect(() => {
    getDivisions({ filter: "" });
    getFormTypes({ filter: "" });
    getAreas({ filter: "" });
    fetchForms();
  }, [searchText, divisionFilter]);

  const handleAddClick = () => {
    if (selectedRow) {
      // Navigate to the route with the selected row's ID
      navigate(ROUTES.CLIFSAADMIN.viewReturnTransmittalForm.parse(selectedRow?.id));
    }
    handleToggleFormModal();
  };

  return (
    <div>
      <DataTable
        className="!min-h-[100%] h-[300px] border-[1px] border-zinc-300 mt-8"
        pagination
        paginationPerPage={10}
        paginationRowsPerPageOptions={[10, 20, 30]}
        fixedHeader
        progressPending={loading}
        fixedHeaderScrollHeight="300px"
        columns={columns}
        data={returnedPads} // Use the properly extracted array
        selectableRowsSingle={true}
        selectableRows={true}
        onSelectedRowsChange={({ selectedRows }) => {
          const selected = selectedRows[0] || null;
          setSelectedRow(selected);
        }}
      />
      <div className="flex justify-end mt-4">
        <Button
          onClick={handleAddClick}
          variant="primary"
          classNames="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
          disabled={!selectedRow} // Disable button if no row is selected
        >
          Add
        </Button>
      </div>
    </div>
  );
};

export default SeriesTransmittalTable;
